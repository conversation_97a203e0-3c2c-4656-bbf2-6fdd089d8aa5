# -*- coding: utf-8 -*-
"""
图形验证码生成和验证模块
"""
import random
import string
import uuid
from io import BytesIO
from PIL import Image, ImageDraw, ImageFont
from django.core.cache import cache
from django.conf import settings
import base64


class CaptchaService:
    """图形验证码服务类"""
    
    # 验证码配置
    CAPTCHA_LENGTH = 4  # 验证码长度
    CAPTCHA_TIMEOUT = 300  # 验证码有效期（秒）
    IMAGE_WIDTH = 120  # 图片宽度
    IMAGE_HEIGHT = 40  # 图片高度
    
    # 字符集（排除容易混淆的字符）
    CHAR_SET = '23456789ABCDEFGHJKLMNPQRSTUVWXYZ'
    
    @classmethod
    def generate_captcha(cls):
        """
        生成图形验证码
        
        Returns:
            tuple: (验证码key, base64编码的图片数据)
        """
        # 生成随机验证码
        captcha_text = ''.join(random.choices(cls.CHAR_SET, k=cls.CAPTCHA_LENGTH))
        
        # 生成唯一key
        captcha_key = str(uuid.uuid4())
        
        # 将验证码存储到缓存中
        cache.set(
            f"captcha:{captcha_key}", 
            captcha_text.upper(), 
            timeout=cls.CAPTCHA_TIMEOUT
        )
        
        # 生成验证码图片
        image_data = cls._create_captcha_image(captcha_text)
        
        return captcha_key, image_data
    
    @classmethod
    def verify_captcha(cls, captcha_key, user_input):
        """
        验证图形验证码
        
        Args:
            captcha_key: 验证码key
            user_input: 用户输入的验证码
            
        Returns:
            bool: 验证是否成功
        """
        if not captcha_key or not user_input:
            return False
        
        # 从缓存中获取正确的验证码
        cache_key = f"captcha:{captcha_key}"
        correct_captcha = cache.get(cache_key)
        
        if not correct_captcha:
            return False
        
        # 验证成功后删除缓存（防止重复使用）
        cache.delete(cache_key)
        
        # 不区分大小写比较
        return correct_captcha.upper() == user_input.upper()
    
    @classmethod
    def _create_captcha_image(cls, text):
        """
        创建验证码图片
        
        Args:
            text: 验证码文本
            
        Returns:
            str: base64编码的图片数据
        """
        # 创建图片
        image = Image.new('RGB', (cls.IMAGE_WIDTH, cls.IMAGE_HEIGHT), color='white')
        draw = ImageDraw.Draw(image)
        
        # 尝试加载字体，如果失败则使用默认字体
        try:
            # 可以根据系统调整字体路径
            font = ImageFont.truetype('arial.ttf', 20)
        except (OSError, IOError):
            try:
                # Windows系统字体路径
                font = ImageFont.truetype('C:/Windows/Fonts/arial.ttf', 20)
            except (OSError, IOError):
                # 使用默认字体
                font = ImageFont.load_default()
        
        # 添加背景噪点
        cls._add_noise_points(draw)
        
        # 绘制验证码文字
        cls._draw_text(draw, text, font)
        
        # 添加干扰线
        cls._add_noise_lines(draw)
        
        # 将图片转换为base64
        buffer = BytesIO()
        image.save(buffer, format='PNG')
        image_data = base64.b64encode(buffer.getvalue()).decode()
        
        return f"data:image/png;base64,{image_data}"
    
    @classmethod
    def _add_noise_points(cls, draw):
        """添加噪点"""
        for _ in range(50):
            x = random.randint(0, cls.IMAGE_WIDTH)
            y = random.randint(0, cls.IMAGE_HEIGHT)
            draw.point((x, y), fill=cls._random_color())
    
    @classmethod
    def _draw_text(cls, draw, text, font):
        """绘制验证码文字"""
        char_width = cls.IMAGE_WIDTH // len(text)
        
        for i, char in enumerate(text):
            # 随机位置和颜色
            x = char_width * i + random.randint(5, 15)
            y = random.randint(5, 15)
            color = cls._random_color()
            
            # 绘制字符
            draw.text((x, y), char, font=font, fill=color)
    
    @classmethod
    def _add_noise_lines(cls, draw):
        """添加干扰线"""
        for _ in range(3):
            start_x = random.randint(0, cls.IMAGE_WIDTH // 2)
            start_y = random.randint(0, cls.IMAGE_HEIGHT)
            end_x = random.randint(cls.IMAGE_WIDTH // 2, cls.IMAGE_WIDTH)
            end_y = random.randint(0, cls.IMAGE_HEIGHT)
            
            draw.line(
                [(start_x, start_y), (end_x, end_y)], 
                fill=cls._random_color(), 
                width=1
            )
    
    @classmethod
    def _random_color(cls):
        """生成随机颜色"""
        return (
            random.randint(0, 150),  # R
            random.randint(0, 150),  # G
            random.randint(0, 150)   # B
        )