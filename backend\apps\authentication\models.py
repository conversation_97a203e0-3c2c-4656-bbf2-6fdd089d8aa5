"""
认证模块 - 会话管理模型
"""
from django.db import models
from apps.common.models import BaseModel


class UserSession(BaseModel):
    """用户会话管理 - 控制并发登录"""
    user = models.ForeignKey('users.UserProfile', on_delete=models.CASCADE, verbose_name="用户")
    session_key = models.CharField(max_length=40, unique=True, verbose_name="会话密钥")
    ip_address = models.GenericIPAddressField(verbose_name="IP地址")
    user_agent = models.TextField(verbose_name="用户代理")
    
    # 会话状态
    is_active = models.BooleanField(default=True, verbose_name="是否活跃")
    last_activity = models.DateTimeField(auto_now=True, verbose_name="最后活动时间")
    expires_at = models.DateTimeField(verbose_name="过期时间")
    
    # 设备信息
    device_type = models.CharField(max_length=50, blank=True, verbose_name="设备类型")
    browser = models.CharField(max_length=100, blank=True, verbose_name="浏览器")
    os = models.CharField(max_length=100, blank=True, verbose_name="操作系统")
    
    class Meta:
        db_table = 'auth_user_session'
        verbose_name = "用户会话"
        verbose_name_plural = "用户会话"
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['expires_at']),
        ]
    
    def __str__(self):
        return f"{self.user.nickname} - {self.ip_address}"