# -*- coding: utf-8 -*-
"""
认证模块测试
"""
import json
from unittest.mock import patch, MagicMock
from django.test import TestCase, override_settings
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.core.cache import cache
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from apps.authentication.captcha import CaptchaService
from apps.authentication.services import AuthenticationService, SessionService
from apps.authentication.models import UserSession

User = get_user_model()


class CaptchaServiceTest(TestCase):
    """验证码服务测试"""
    
    def setUp(self):
        """测试前准备"""
        cache.clear()
    
    def test_generate_captcha(self):
        """测试生成验证码"""
        captcha_key, captcha_image = CaptchaService.generate_captcha()
        
        # 验证返回值
        self.assertIsNotNone(captcha_key)
        self.assertIsNotNone(captcha_image)
        self.assertTrue(captcha_image.startswith('data:image/png;base64,'))
        
        # 验证缓存中存在验证码
        cached_captcha = cache.get(f"captcha:{captcha_key}")
        self.assertIsNotNone(cached_captcha)
        self.assertEqual(len(cached_captcha), CaptchaService.CAPTCHA_LENGTH)
    
    def test_verify_captcha_success(self):
        """测试验证码验证成功"""
        # 生成验证码
        captcha_key, _ = CaptchaService.generate_captcha()
        correct_captcha = cache.get(f"captcha:{captcha_key}")
        
        # 验证成功
        result = CaptchaService.verify_captcha(captcha_key, correct_captcha)
        self.assertTrue(result)
        
        # 验证码应该被删除
        cached_captcha = cache.get(f"captcha:{captcha_key}")
        self.assertIsNone(cached_captcha)
    
    def test_verify_captcha_failure(self):
        """测试验证码验证失败"""
        # 生成验证码
        captcha_key, _ = CaptchaService.generate_captcha()
        
        # 使用错误的验证码
        result = CaptchaService.verify_captcha(captcha_key, "WRONG")
        self.assertFalse(result)
    
    def test_verify_captcha_expired(self):
        """测试过期验证码验证"""
        # 验证不存在的key
        result = CaptchaService.verify_captcha("nonexistent", "1234")
        self.assertFalse(result)
    
    def test_verify_captcha_case_insensitive(self):
        """测试验证码大小写不敏感"""
        captcha_key, _ = CaptchaService.generate_captcha()
        correct_captcha = cache.get(f"captcha:{captcha_key}")
        
        # 使用小写验证
        result = CaptchaService.verify_captcha(captcha_key, correct_captcha.lower())
        self.assertTrue(result)


class SessionServiceTest(TestCase):
    """会话服务测试"""
    
    def setUp(self):
        """测试前准备"""
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            nickname='测试用户'
        )
        
        # 模拟请求对象
        self.mock_request = MagicMock()
        self.mock_request.META = {
            'HTTP_USER_AGENT': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'REMOTE_ADDR': '127.0.0.1'
        }
    
    def test_create_session(self):
        """测试创建会话"""
        session = SessionService.create_session(self.user, self.mock_request)
        
        self.assertIsNotNone(session)
        self.assertEqual(session.user, self.user)
        self.assertTrue(session.is_active)
        self.assertEqual(session.ip_address, '127.0.0.1')
        self.assertIsNotNone(session.session_key)
    
    @override_settings(MAX_USER_SESSIONS=2)
    def test_session_limit(self):
        """测试会话数量限制"""
        # 创建3个会话，应该只保留2个
        sessions = []
        for i in range(3):
            session = SessionService.create_session(self.user, self.mock_request)
            sessions.append(session)
        
        # 检查活跃会话数量
        active_sessions = UserSession.objects.filter(
            user=self.user,
            is_active=True,
            is_deleted=False
        ).count()
        
        self.assertEqual(active_sessions, 2)
        
        # 第一个会话应该被踢出
        sessions[0].refresh_from_db()
        self.assertFalse(sessions[0].is_active)
    
    def test_get_client_ip(self):
        """测试获取客户端IP"""
        # 测试X-Forwarded-For头
        request = MagicMock()
        request.META = {'HTTP_X_FORWARDED_FOR': '***********, ********'}
        ip = SessionService.get_client_ip(request)
        self.assertEqual(ip, '***********')
        
        # 测试REMOTE_ADDR
        request.META = {'REMOTE_ADDR': '127.0.0.1'}
        ip = SessionService.get_client_ip(request)
        self.assertEqual(ip, '127.0.0.1')
    
    def test_get_device_type(self):
        """测试获取设备类型"""
        # 测试桌面设备
        request = MagicMock()
        request.META = {'HTTP_USER_AGENT': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)'}
        device_type = SessionService.get_device_type(request)
        self.assertEqual(device_type, 'desktop')
        
        # 测试移动设备
        request.META = {'HTTP_USER_AGENT': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0)'}
        device_type = SessionService.get_device_type(request)
        self.assertEqual(device_type, 'mobile')
    
    def test_cleanup_expired_sessions(self):
        """测试清理过期会话"""
        # 创建一个过期会话
        session = SessionService.create_session(self.user, self.mock_request)
        session.expires_at = session.created_at  # 设置为已过期
        session.save()
        
        # 清理过期会话
        cleaned_count = SessionService.cleanup_expired_sessions()
        
        self.assertEqual(cleaned_count, 1)
        session.refresh_from_db()
        self.assertFalse(session.is_active)


class AuthenticationServiceTest(TestCase):
    """认证服务测试"""
    
    def setUp(self):
        """测试前准备"""
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            nickname='测试用户'
        )
        
        self.mock_request = MagicMock()
        self.mock_request.META = {
            'HTTP_USER_AGENT': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
            'REMOTE_ADDR': '127.0.0.1'
        }
    
    def test_authenticate_user_success(self):
        """测试用户认证成功"""
        user, tokens = AuthenticationService.authenticate_user(
            'testuser', 'testpass123', self.mock_request
        )
        
        self.assertEqual(user, self.user)
        self.assertIn('access', tokens)
        self.assertIn('refresh', tokens)
        self.assertIn('access_expires_in', tokens)
        self.assertIn('refresh_expires_in', tokens)
    
    def test_authenticate_user_wrong_password(self):
        """测试错误密码认证"""
        from apps.common.exceptions import BusinessException, ErrorCode
        
        with self.assertRaises(BusinessException) as context:
            AuthenticationService.authenticate_user(
                'testuser', 'wrongpass', self.mock_request
            )
        
        self.assertEqual(context.exception.code, ErrorCode.LOGIN_FAILED)
    
    def test_authenticate_user_nonexistent(self):
        """测试不存在的用户认证"""
        from apps.common.exceptions import BusinessException, ErrorCode
        
        with self.assertRaises(BusinessException) as context:
            AuthenticationService.authenticate_user(
                'nonexistent', 'password', self.mock_request
            )
        
        self.assertEqual(context.exception.code, ErrorCode.LOGIN_FAILED)
    
    def test_authenticate_disabled_user(self):
        """测试被禁用用户认证"""
        from apps.common.exceptions import BusinessException, ErrorCode
        
        self.user.is_active = False
        self.user.save()
        
        with self.assertRaises(BusinessException) as context:
            AuthenticationService.authenticate_user(
                'testuser', 'testpass123', self.mock_request
            )
        
        self.assertEqual(context.exception.code, ErrorCode.ACCOUNT_DISABLED)
    
    def test_account_lockout(self):
        """测试账户锁定机制"""
        from apps.common.exceptions import BusinessException, ErrorCode
        
        # 连续失败登录
        for i in range(AuthenticationService.MAX_LOGIN_ATTEMPTS):
            try:
                AuthenticationService.authenticate_user(
                    'testuser', 'wrongpass', self.mock_request
                )
            except BusinessException:
                pass
        
        # 账户应该被锁定
        self.user.refresh_from_db()
        self.assertIsNotNone(self.user.locked_until)
        
        # 再次尝试登录应该提示账户被锁定
        with self.assertRaises(BusinessException) as context:
            AuthenticationService.authenticate_user(
                'testuser', 'testpass123', self.mock_request
            )
        
        self.assertEqual(context.exception.code, ErrorCode.ACCOUNT_LOCKED)
    
    def test_refresh_token_success(self):
        """测试令牌刷新成功"""
        # 生成刷新令牌
        refresh = RefreshToken.for_user(self.user)
        refresh_token = str(refresh)
        
        # 刷新令牌
        tokens = AuthenticationService.refresh_token(refresh_token)
        
        self.assertIn('access', tokens)
        self.assertIn('access_expires_in', tokens)
    
    def test_refresh_token_invalid(self):
        """测试无效令牌刷新"""
        from apps.common.exceptions import BusinessException, ErrorCode
        
        with self.assertRaises(BusinessException) as context:
            AuthenticationService.refresh_token('invalid_token')
        
        self.assertEqual(context.exception.code, ErrorCode.TOKEN_INVALID)


class AuthenticationAPITest(APITestCase):
    """认证API测试"""
    
    def setUp(self):
        """测试前准备"""
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            nickname='测试用户'
        )
        cache.clear()
    
    def test_get_captcha(self):
        """测试获取验证码API"""
        url = reverse('authentication:captcha')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertEqual(data['code'], 1000)
        self.assertIn('captcha_key', data['data'])
        self.assertIn('captcha_image', data['data'])
        self.assertIn('expires_in', data['data'])
    
    def test_login_success(self):
        """测试登录成功"""
        # 先获取验证码
        captcha_url = reverse('authentication:captcha')
        captcha_response = self.client.get(captcha_url)
        captcha_data = captcha_response.json()
        captcha_key = captcha_data['data']['captcha_key']
        
        # 从缓存获取正确的验证码
        from django.core.cache import cache
        correct_captcha = cache.get(f"captcha:{captcha_key}")
        
        url = reverse('authentication:login')
        data = {
            'username': 'testuser',
            'password': 'testpass123',
            'captcha': correct_captcha,
            'captcha_key': captcha_key
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        
        self.assertEqual(response_data['code'], 1000)
        self.assertIn('access', response_data['data'])
        self.assertIn('refresh', response_data['data'])
        self.assertIn('user_info', response_data['data'])
    
    def test_login_wrong_captcha(self):
        """测试验证码错误"""
        # 先获取验证码
        captcha_url = reverse('authentication:captcha')
        captcha_response = self.client.get(captcha_url)
        captcha_data = captcha_response.json()
        captcha_key = captcha_data['data']['captcha_key']
        
        url = reverse('authentication:login')
        data = {
            'username': 'testuser',
            'password': 'testpass123',
            'captcha': 'WRONG',
            'captcha_key': captcha_key
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        response_data = response.json()
        self.assertEqual(response_data['code'], 2002)  # CAPTCHA_ERROR
    
    def test_login_missing_fields(self):
        """测试登录缺少必填字段"""
        url = reverse('authentication:login')
        data = {
            'username': 'testuser',
            # 缺少password, captcha, captcha_key
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_refresh_token_success(self):
        """测试令牌刷新成功"""
        # 生成刷新令牌
        refresh = RefreshToken.for_user(self.user)
        
        url = reverse('authentication:refresh_token')
        data = {'refresh': str(refresh)}
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        
        self.assertEqual(response_data['code'], 1000)
        self.assertIn('access', response_data['data'])
    
    def test_refresh_token_invalid(self):
        """测试无效令牌刷新"""
        url = reverse('authentication:refresh_token')
        data = {'refresh': 'invalid_token'}
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_logout_success(self):
        """测试登出成功"""
        # 先登录获取令牌
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        url = reverse('authentication:logout')
        response = self.client.post(url, {}, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        self.assertEqual(response_data['code'], 1000)
    
    def test_logout_unauthenticated(self):
        """测试未认证用户登出"""
        url = reverse('authentication:logout')
        response = self.client.post(url, {}, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_get_user_info_success(self):
        """测试获取用户信息成功"""
        # 先登录获取令牌
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        url = reverse('authentication:user_info')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        
        self.assertEqual(response_data['code'], 1000)
        self.assertEqual(response_data['data']['username'], 'testuser')
        self.assertEqual(response_data['data']['nickname'], '测试用户')
    
    def test_get_user_info_unauthenticated(self):
        """测试未认证用户获取信息"""
        url = reverse('authentication:user_info')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)


class UserSessionViewSetTest(APITestCase):
    """用户会话视图集测试"""
    
    def setUp(self):
        """测试前准备"""
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            nickname='测试用户'
        )
        
        # 创建测试会话
        self.session = UserSession.objects.create(
            user=self.user,
            session_key='test-session-key',
            ip_address='127.0.0.1',
            user_agent='Test Agent',
            expires_at='2024-12-31 23:59:59'
        )
        
        # 认证用户
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
    
    def test_list_sessions(self):
        """测试获取会话列表"""
        url = reverse('authentication:user-session-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        
        self.assertEqual(response_data['code'], 1000)
        self.assertEqual(len(response_data['data']), 1)
        self.assertEqual(response_data['data'][0]['session_key'], 'test-session-key')
    
    def test_terminate_session(self):
        """测试强制下线会话"""
        url = reverse('authentication:user-session-terminate', kwargs={'pk': self.session.pk})
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        self.assertEqual(response_data['code'], 1000)
        
        # 验证会话已失效
        self.session.refresh_from_db()
        self.assertFalse(self.session.is_active)
    
    def test_cleanup_expired_sessions(self):
        """测试清理过期会话"""
        url = reverse('authentication:user-session-cleanup-expired')
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        self.assertEqual(response_data['code'], 1000)