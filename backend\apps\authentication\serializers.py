# -*- coding: utf-8 -*-
"""
认证模块序列化器
"""
from rest_framework import serializers
from django.contrib.auth import get_user_model
from apps.authentication.models import UserSession

User = get_user_model()


class LoginSerializer(serializers.Serializer):
    """用户登录序列化器"""
    
    username = serializers.CharField(
        max_length=150,
        required=True,
        error_messages={
            'required': '用户名不能为空',
            'max_length': '用户名长度不能超过150个字符'
        }
    )
    
    password = serializers.CharField(
        max_length=128,
        required=True,
        write_only=True,
        error_messages={
            'required': '密码不能为空',
            'max_length': '密码长度不能超过128个字符'
        }
    )
    
    captcha = serializers.CharField(
        max_length=10,
        required=True,
        error_messages={
            'required': '验证码不能为空',
            'max_length': '验证码长度不能超过10个字符'
        }
    )
    
    captcha_key = serializers.Char<PERSON>ield(
        max_length=50,
        required=True,
        error_messages={
            'required': '验证码key不能为空',
            'max_length': '验证码key长度不能超过50个字符'
        }
    )
    
    def validate_username(self, value):
        """验证用户名格式"""
        if not value.strip():
            raise serializers.ValidationError("用户名不能为空白字符")
        return value.strip()
    
    def validate_password(self, value):
        """验证密码格式"""
        if not value:
            raise serializers.ValidationError("密码不能为空")
        return value
    
    def validate_captcha(self, value):
        """验证验证码格式"""
        if not value.strip():
            raise serializers.ValidationError("验证码不能为空白字符")
        return value.strip().upper()


class TokenRefreshSerializer(serializers.Serializer):
    """令牌刷新序列化器"""
    
    refresh = serializers.CharField(
        required=True,
        error_messages={
            'required': '刷新令牌不能为空'
        }
    )
    
    def validate_refresh(self, value):
        """验证刷新令牌格式"""
        if not value.strip():
            raise serializers.ValidationError("刷新令牌不能为空白字符")
        return value.strip()


class UserSessionSerializer(serializers.ModelSerializer):
    """用户会话序列化器"""
    
    user_nickname = serializers.CharField(source='user.nickname', read_only=True)
    user_username = serializers.CharField(source='user.username', read_only=True)
    
    class Meta:
        model = UserSession
        fields = [
            'id', 'session_key', 'ip_address', 'user_agent',
            'is_active', 'last_activity', 'expires_at',
            'device_type', 'browser', 'os', 'created_at',
            'user_nickname', 'user_username'
        ]
        read_only_fields = ['id', 'created_at', 'user_nickname', 'user_username']


class LoginResponseSerializer(serializers.Serializer):
    """登录响应序列化器"""
    
    access = serializers.CharField(help_text="访问令牌")
    refresh = serializers.CharField(help_text="刷新令牌")
    access_expires_in = serializers.IntegerField(help_text="访问令牌过期时间（秒）")
    refresh_expires_in = serializers.IntegerField(help_text="刷新令牌过期时间（秒）")
    
    user_info = serializers.DictField(help_text="用户基本信息")


class UserInfoSerializer(serializers.ModelSerializer):
    """用户信息序列化器（用于登录响应）"""
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'nickname', 'email', 'phone',
            'avatar', 'is_active', 'last_login_time', 'last_login_ip'
        ]
        read_only_fields = fields


class CaptchaResponseSerializer(serializers.Serializer):
    """验证码响应序列化器"""
    
    captcha_key = serializers.CharField(help_text="验证码唯一标识")
    captcha_image = serializers.CharField(help_text="base64编码的验证码图片")
    expires_in = serializers.IntegerField(help_text="验证码过期时间（秒）")


class LogoutSerializer(serializers.Serializer):
    """登出序列化器"""
    
    # 可以添加一些可选参数，比如是否登出所有设备
    logout_all_devices = serializers.BooleanField(
        default=False,
        required=False,
        help_text="是否登出所有设备"
    )