"""
公共模块 - 中间件
"""
from django.utils.deprecation import MiddlewareMixin
from django.http import JsonResponse
from django.utils import timezone
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from apps.audit.models import OperationLog
import time
import json


class JWTAuthenticationMiddleware(MiddlewareMixin):
    """JWT认证中间件"""
    
    def process_request(self, request):
        # 跳过不需要认证的路径
        skip_paths = [
            '/api/auth/login',
            '/api/auth/captcha',
            '/api/auth/refresh',
            '/admin/',
            '/static/',
            '/media/',
        ]
        
        # 检查是否需要跳过认证
        for skip_path in skip_paths:
            if request.path.startswith(skip_path):
                return None
        
        # JWT令牌验证
        jwt_auth = JWTAuthentication()
        try:
            user_token = jwt_auth.authenticate(request)
            if user_token:
                user, token = user_token
                request.user = user
                request.token = token
        except (InvalidToken, TokenError):
            # 对于API请求返回401
            if request.path.startswith('/api/'):
                return JsonResponse({
                    'code': 2003,
                    'message': '令牌无效或已过期',
                    'data': None,
                    'timestamp': timezone.now().isoformat()
                }, status=401)
        
        return None


class PermissionMiddleware(MiddlewareMixin):
    """权限验证中间件"""
    
    def process_request(self, request):
        # 跳过不需要权限验证的路径
        skip_paths = [
            '/api/auth/',
            '/admin/',
            '/static/',
            '/media/',
        ]
        
        for skip_path in skip_paths:
            if request.path.startswith(skip_path):
                return None
        
        # 检查用户权限
        if hasattr(request, 'user') and request.user.is_authenticated:
            # 这里可以添加具体的权限验证逻辑
            # 暂时允许所有已认证用户访问
            pass
        elif request.path.startswith('/api/'):
            return JsonResponse({
                'code': 2101,
                'message': '权限不足',
                'data': None,
                'timestamp': timezone.now().isoformat()
            }, status=403)
        
        return None


class AuditLogMiddleware(MiddlewareMixin):
    """审计日志中间件"""
    
    def process_request(self, request):
        # 记录请求开始时间
        request._start_time = time.time()
        return None
    
    def process_response(self, request, response):
        # 跳过不需要记录的路径
        skip_paths = [
            '/static/',
            '/media/',
            '/api/auth/captcha',
        ]
        
        for skip_path in skip_paths:
            if request.path.startswith(skip_path):
                return response
        
        # 记录操作日志
        if hasattr(request, 'user') and request.user.is_authenticated:
            try:
                # 计算响应时间
                response_time = int((time.time() - getattr(request, '_start_time', time.time())) * 1000)
                
                # 确定操作类型
                operation_type = self._get_operation_type(request.method)
                
                # 生成操作描述
                operation_desc = self._get_operation_desc(request)
                
                # 异步记录日志（这里先同步记录，后续可以改为异步）
                OperationLog.objects.create(
                    user=request.user,
                    operation_type=operation_type,
                    operation_desc=operation_desc,
                    method=request.method,
                    path=request.path,
                    ip_address=self._get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', '')[:500],
                    status_code=response.status_code,
                    response_time=response_time
                )
            except Exception as e:
                # 记录日志失败不应该影响正常响应
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"记录审计日志失败: {e}")
        
        return response
    
    def _get_operation_type(self, method):
        """根据HTTP方法确定操作类型"""
        method_mapping = {
            'GET': 'QUERY',
            'POST': 'CREATE',
            'PUT': 'UPDATE',
            'PATCH': 'UPDATE',
            'DELETE': 'DELETE',
        }
        return method_mapping.get(method, 'QUERY')
    
    def _get_operation_desc(self, request):
        """生成操作描述"""
        path = request.path
        method = request.method
        
        # 简化的操作描述生成
        if '/api/auth/login' in path:
            return '用户登录'
        elif '/api/auth/logout' in path:
            return '用户登出'
        elif '/api/users/' in path:
            if method == 'GET':
                return '查询用户信息'
            elif method == 'POST':
                return '创建用户'
            elif method in ['PUT', 'PATCH']:
                return '更新用户信息'
            elif method == 'DELETE':
                return '删除用户'
        elif '/api/departments/' in path:
            if method == 'GET':
                return '查询部门信息'
            elif method == 'POST':
                return '创建部门'
            elif method in ['PUT', 'PATCH']:
                return '更新部门信息'
            elif method == 'DELETE':
                return '删除部门'
        elif '/api/roles/' in path:
            if method == 'GET':
                return '查询角色信息'
            elif method == 'POST':
                return '创建角色'
            elif method in ['PUT', 'PATCH']:
                return '更新角色信息'
            elif method == 'DELETE':
                return '删除角色'
        
        return f"{method} {path}"
    
    def _get_client_ip(self, request):
        """获取客户端IP"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', '')
        return ip