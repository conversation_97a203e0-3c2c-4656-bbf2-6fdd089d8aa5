# -*- coding: utf-8 -*-
"""
认证模块简单视图
"""
import logging
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth import authenticate
from rest_framework_simplejwt.tokens import RefreshToken
from apps.common.response import ApiResponse
from apps.common.exceptions import BusinessException, ErrorCode

logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([AllowAny])
def get_captcha(request):
    """获取图形验证码"""
    try:
        import uuid
        import random
        import string
        from django.core.cache import cache
        
        # 生成简单的验证码
        captcha_text = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
        captcha_key = str(uuid.uuid4())
        
        # 存储到缓存
        cache.set(f"captcha:{captcha_key}", captcha_text.upper(), timeout=300)
        print(f"DEBUG: 生成验证码 {captcha_text} 存储到 captcha:{captcha_key}")
        
        data = {
            'captcha_key': captcha_key,
            'captcha_image': f"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjQwIj48dGV4dCB4PSIxMCIgeT0iMjUiIGZvbnQtc2l6ZT0iMjAiPnt0ZXh0fTwvdGV4dD48L3N2Zz4=".replace('{text}', captcha_text),
            'expires_in': 300
        }
        
        return ApiResponse.success(data=data, message="验证码生成成功")
        
    except Exception as e:
        logger.error(f"生成验证码失败: {str(e)}", exc_info=True)
        return ApiResponse.error(message="验证码生成失败，请稍后重试")


@api_view(['POST'])
@permission_classes([AllowAny])
def login(request):
    """用户登录"""
    try:
        from django.core.cache import cache
        
        username = request.data.get('username')
        password = request.data.get('password')
        captcha = request.data.get('captcha')
        captcha_key = request.data.get('captcha_key')
        
        if not all([username, password, captcha, captcha_key]):
            return ApiResponse.error(message="请填写完整的登录信息")
        
        # 验证验证码
        cached_captcha = cache.get(f"captcha:{captcha_key}")
        print(f"DEBUG: 缓存中的验证码: {cached_captcha}, 用户输入: {captcha}")
        if not cached_captcha or cached_captcha.upper() != captcha.upper():
            return ApiResponse.error(message="验证码错误或已过期", code=ErrorCode.CAPTCHA_ERROR)
        
        # 删除已使用的验证码
        cache.delete(f"captcha:{captcha_key}")
        
        # 用户认证
        user = authenticate(request, username=username, password=password)
        if not user:
            return ApiResponse.error(message="用户名或密码错误", code=ErrorCode.LOGIN_FAILED)
        
        if not user.is_active:
            return ApiResponse.error(message="账户已被禁用", code=ErrorCode.ACCOUNT_DISABLED)
        
        # 生成JWT令牌
        refresh = RefreshToken.for_user(user)
        
        response_data = {
            'access': str(refresh.access_token),
            'refresh': str(refresh),
            'user_info': {
                'id': user.id,
                'username': user.username,
                'nickname': user.nickname,
                'email': user.email,
            }
        }
        
        return ApiResponse.success(data=response_data, message="登录成功")
        
    except Exception as e:
        logger.error(f"用户登录异常: {str(e)}", exc_info=True)
        return ApiResponse.error(message="登录失败，请稍后重试")


@api_view(['POST'])
@permission_classes([AllowAny])
def refresh_token(request):
    """刷新JWT访问令牌"""
    try:
        refresh_token = request.data.get('refresh')
        if not refresh_token:
            return ApiResponse.error(message="刷新令牌不能为空")
        
        refresh = RefreshToken(refresh_token)
        
        response_data = {
            'access': str(refresh.access_token),
        }
        
        return ApiResponse.success(data=response_data, message="令牌刷新成功")
        
    except Exception as e:
        logger.error(f"令牌刷新异常: {str(e)}", exc_info=True)
        return ApiResponse.error(message="令牌刷新失败", code=ErrorCode.TOKEN_INVALID)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def logout(request):
    """用户登出"""
    try:
        return ApiResponse.success(message="登出成功")
        
    except Exception as e:
        logger.error(f"用户登出异常: {str(e)}", exc_info=True)
        return ApiResponse.error(message="登出失败，请稍后重试")


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_info(request):
    """获取当前用户信息"""
    try:
        user_data = {
            'id': request.user.id,
            'username': request.user.username,
            'nickname': request.user.nickname,
            'email': request.user.email,
            'phone': request.user.phone,
            'avatar': request.user.avatar,
            'is_active': request.user.is_active,
            'last_login_time': request.user.last_login_time,
            'last_login_ip': request.user.last_login_ip,
        }
        
        return ApiResponse.success(data=user_data, message="获取用户信息成功")
        
    except Exception as e:
        logger.error(f"获取用户信息异常: {str(e)}", exc_info=True)
        return ApiResponse.error(message="获取用户信息失败")