# -*- coding: utf-8 -*-
"""
认证模块基础功能测试
"""
import json
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.core.cache import cache
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()


class BasicAuthenticationTest(APITestCase):
    """基础认证功能测试"""
    
    def setUp(self):
        """测试前准备"""
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            nickname='测试用户'
        )
        cache.clear()
    
    def test_captcha_generation(self):
        """测试验证码生成"""
        url = reverse('authentication:captcha')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertEqual(data['code'], 1000)
        self.assertIn('captcha_key', data['data'])
        self.assertIn('captcha_image', data['data'])
        self.assertIn('expires_in', data['data'])
    
    def test_login_with_correct_captcha(self):
        """测试使用正确验证码登录"""
        # 先获取验证码
        captcha_url = reverse('authentication:captcha')
        captcha_response = self.client.get(captcha_url)
        captcha_data = captcha_response.json()
        captcha_key = captcha_data['data']['captcha_key']
        
        # 从缓存获取正确的验证码
        correct_captcha = cache.get(f"captcha:{captcha_key}")
        
        # 登录
        login_url = reverse('authentication:login')
        login_data = {
            'username': 'testuser',
            'password': 'testpass123',
            'captcha': correct_captcha,
            'captcha_key': captcha_key
        }
        
        response = self.client.post(login_url, login_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertEqual(data['code'], 1000)
        self.assertIn('access', data['data'])
        self.assertIn('refresh', data['data'])
        self.assertIn('user_info', data['data'])
        self.assertEqual(data['data']['user_info']['username'], 'testuser')
    
    def test_login_with_wrong_captcha(self):
        """测试使用错误验证码登录"""
        # 先获取验证码
        captcha_url = reverse('authentication:captcha')
        captcha_response = self.client.get(captcha_url)
        captcha_data = captcha_response.json()
        captcha_key = captcha_data['data']['captcha_key']
        
        # 登录（使用错误验证码）
        login_url = reverse('authentication:login')
        login_data = {
            'username': 'testuser',
            'password': 'testpass123',
            'captcha': 'WRONG',
            'captcha_key': captcha_key
        }
        
        response = self.client.post(login_url, login_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        data = response.json()
        self.assertEqual(data['code'], 2002)  # CAPTCHA_ERROR
    
    def test_get_user_info_with_token(self):
        """测试使用令牌获取用户信息"""
        # 生成JWT令牌
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        # 获取用户信息
        url = reverse('authentication:user_info')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertEqual(data['code'], 1000)
        self.assertEqual(data['data']['username'], 'testuser')
        self.assertEqual(data['data']['nickname'], '测试用户')
    
    def test_get_user_info_without_token(self):
        """测试未认证获取用户信息"""
        url = reverse('authentication:user_info')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_token_refresh(self):
        """测试令牌刷新"""
        # 生成刷新令牌
        refresh = RefreshToken.for_user(self.user)
        
        url = reverse('authentication:refresh_token')
        data = {'refresh': str(refresh)}
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        
        self.assertEqual(response_data['code'], 1000)
        self.assertIn('access', response_data['data'])
    
    def test_logout(self):
        """测试登出"""
        # 生成JWT令牌
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        url = reverse('authentication:logout')
        response = self.client.post(url, {}, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(data['code'], 1000)