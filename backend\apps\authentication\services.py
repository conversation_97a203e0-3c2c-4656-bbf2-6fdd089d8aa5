# -*- coding: utf-8 -*-
"""
认证服务模块
"""
import uuid
import logging
from datetime import timedelta
from django.utils import timezone
from django.conf import settings
from django.contrib.auth import authenticate
from django.core.cache import cache
from rest_framework_simplejwt.tokens import RefreshToken
from apps.authentication.models import UserSession
from apps.common.exceptions import BusinessException, ErrorCode

logger = logging.getLogger(__name__)


class SessionService:
    """会话管理服务类"""
    
    @staticmethod
    def create_session(user, request):
        """
        创建用户会话
        
        Args:
            user: 用户对象
            request: HTTP请求对象
            
        Returns:
            UserSession: 创建的会话对象
        """
        # 检查并发登录限制
        max_sessions = getattr(settings, 'MAX_USER_SESSIONS', 5)
        active_sessions = UserSession.objects.filter(
            user=user, 
            is_active=True,
            expires_at__gt=timezone.now(),
            is_deleted=False
        ).count()
        
        if active_sessions >= max_sessions:
            # 踢出最早的会话
            oldest_session = UserSession.objects.filter(
                user=user, 
                is_active=True,
                is_deleted=False
            ).order_by('created_at').first()
            
            if oldest_session:
                oldest_session.is_active = False
                oldest_session.save()
                logger.info(f"用户 {user.username} 达到最大会话数限制，踢出最早会话")
        
        # 创建新会话
        session = UserSession.objects.create(
            user=user,
            session_key=str(uuid.uuid4()),
            ip_address=SessionService.get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            expires_at=timezone.now() + timedelta(hours=24),
            device_type=SessionService.get_device_type(request),
            browser=SessionService.get_browser(request),
            os=SessionService.get_os(request)
        )
        
        logger.info(f"为用户 {user.username} 创建新会话: {session.session_key}")
        return session
    
    @staticmethod
    def get_client_ip(request):
        """
        获取客户端IP地址
        
        Args:
            request: HTTP请求对象
            
        Returns:
            str: 客户端IP地址
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', '127.0.0.1')
        return ip
    
    @staticmethod
    def get_device_type(request):
        """
        获取设备类型
        
        Args:
            request: HTTP请求对象
            
        Returns:
            str: 设备类型
        """
        user_agent = request.META.get('HTTP_USER_AGENT', '').lower()
        if 'mobile' in user_agent or 'android' in user_agent or 'iphone' in user_agent:
            return 'mobile'
        elif 'tablet' in user_agent or 'ipad' in user_agent:
            return 'tablet'
        return 'desktop'
    
    @staticmethod
    def get_browser(request):
        """
        获取浏览器信息
        
        Args:
            request: HTTP请求对象
            
        Returns:
            str: 浏览器名称
        """
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        if 'Chrome' in user_agent and 'Edge' not in user_agent:
            return 'Chrome'
        elif 'Firefox' in user_agent:
            return 'Firefox'
        elif 'Safari' in user_agent and 'Chrome' not in user_agent:
            return 'Safari'
        elif 'Edge' in user_agent:
            return 'Edge'
        elif 'Opera' in user_agent:
            return 'Opera'
        return 'Unknown'
    
    @staticmethod
    def get_os(request):
        """
        获取操作系统信息
        
        Args:
            request: HTTP请求对象
            
        Returns:
            str: 操作系统名称
        """
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        if 'Windows NT 10' in user_agent:
            return 'Windows 10'
        elif 'Windows NT' in user_agent:
            return 'Windows'
        elif 'Mac OS X' in user_agent:
            return 'macOS'
        elif 'Linux' in user_agent:
            return 'Linux'
        elif 'Android' in user_agent:
            return 'Android'
        elif 'iPhone OS' in user_agent or 'iOS' in user_agent:
            return 'iOS'
        return 'Unknown'
    
    @staticmethod
    def cleanup_expired_sessions():
        """
        清理过期会话
        
        Returns:
            int: 清理的会话数量
        """
        expired_count = UserSession.objects.filter(
            expires_at__lt=timezone.now()
        ).update(is_active=False)
        
        if expired_count > 0:
            logger.info(f"清理了 {expired_count} 个过期会话")
        
        return expired_count
    
    @staticmethod
    def invalidate_user_sessions(user, exclude_session_key=None):
        """
        使用户的所有会话失效
        
        Args:
            user: 用户对象
            exclude_session_key: 要排除的会话key（通常是当前会话）
            
        Returns:
            int: 失效的会话数量
        """
        queryset = UserSession.objects.filter(
            user=user,
            is_active=True,
            is_deleted=False
        )
        
        if exclude_session_key:
            queryset = queryset.exclude(session_key=exclude_session_key)
        
        invalidated_count = queryset.update(is_active=False)
        
        if invalidated_count > 0:
            logger.info(f"使用户 {user.username} 的 {invalidated_count} 个会话失效")
        
        return invalidated_count


class AuthenticationService:
    """认证服务类"""
    
    # 账户锁定配置
    MAX_LOGIN_ATTEMPTS = 5  # 最大登录尝试次数
    LOCKOUT_DURATION = 30  # 锁定时长（分钟）
    
    @classmethod
    def authenticate_user(cls, username, password, request):
        """
        用户认证
        
        Args:
            username: 用户名
            password: 密码
            request: HTTP请求对象
            
        Returns:
            tuple: (用户对象, JWT令牌字典)
            
        Raises:
            BusinessException: 认证失败时抛出异常
        """
        # 检查账户是否被锁定
        user = cls._get_user_by_username(username)
        if user:
            cls._check_account_lockout(user)
        
        # 进行用户认证
        authenticated_user = authenticate(request, username=username, password=password)
        
        if authenticated_user is None:
            # 认证失败，增加失败次数
            if user:
                cls._handle_login_failure(user)
            raise BusinessException(
                code=ErrorCode.LOGIN_FAILED,
                message="用户名或密码错误"
            )
        
        # 检查用户是否被禁用
        if not authenticated_user.is_active:
            raise BusinessException(
                code=ErrorCode.ACCOUNT_DISABLED,
                message="账户已被禁用，请联系管理员"
            )
        
        # 认证成功，重置失败次数
        cls._reset_login_failures(authenticated_user)
        
        # 更新最后登录信息
        cls._update_last_login_info(authenticated_user, request)
        
        # 生成JWT令牌
        tokens = cls._generate_tokens(authenticated_user)
        
        # 创建会话记录
        SessionService.create_session(authenticated_user, request)
        
        logger.info(f"用户 {username} 登录成功，IP: {SessionService.get_client_ip(request)}")
        
        return authenticated_user, tokens
    
    @classmethod
    def _get_user_by_username(cls, username):
        """根据用户名获取用户对象"""
        from apps.users.models import UserProfile
        try:
            return UserProfile.objects.get(username=username)
        except UserProfile.DoesNotExist:
            return None
    
    @classmethod
    def _check_account_lockout(cls, user):
        """检查账户是否被锁定"""
        if user.locked_until and user.locked_until > timezone.now():
            remaining_time = user.locked_until - timezone.now()
            minutes = int(remaining_time.total_seconds() / 60)
            raise BusinessException(
                code=ErrorCode.ACCOUNT_LOCKED,
                message=f"账户已被锁定，请在 {minutes} 分钟后重试"
            )
    
    @classmethod
    def _handle_login_failure(cls, user):
        """处理登录失败"""
        user.login_fail_count += 1
        
        if user.login_fail_count >= cls.MAX_LOGIN_ATTEMPTS:
            # 锁定账户
            user.locked_until = timezone.now() + timedelta(minutes=cls.LOCKOUT_DURATION)
            logger.warning(f"用户 {user.username} 登录失败次数过多，账户已被锁定")
        
        user.save(update_fields=['login_fail_count', 'locked_until'])
    
    @classmethod
    def _reset_login_failures(cls, user):
        """重置登录失败次数"""
        if user.login_fail_count > 0 or user.locked_until:
            user.login_fail_count = 0
            user.locked_until = None
            user.save(update_fields=['login_fail_count', 'locked_until'])
    
    @classmethod
    def _update_last_login_info(cls, user, request):
        """更新最后登录信息"""
        user.last_login_time = timezone.now()
        user.last_login_ip = SessionService.get_client_ip(request)
        user.save(update_fields=['last_login_time', 'last_login_ip'])
    
    @classmethod
    def _generate_tokens(cls, user):
        """生成JWT令牌"""
        refresh = RefreshToken.for_user(user)
        return {
            'access': str(refresh.access_token),
            'refresh': str(refresh),
            'access_expires_in': settings.SIMPLE_JWT['ACCESS_TOKEN_LIFETIME'].total_seconds(),
            'refresh_expires_in': settings.SIMPLE_JWT['REFRESH_TOKEN_LIFETIME'].total_seconds(),
        }
    
    @classmethod
    def logout_user(cls, user, request):
        """
        用户登出
        
        Args:
            user: 用户对象
            request: HTTP请求对象
        """
        # 获取当前会话并设为非活跃
        session_key = request.META.get('HTTP_SESSION_KEY')  # 如果有的话
        
        # 使当前用户的活跃会话失效（可以选择只失效当前会话或所有会话）
        SessionService.invalidate_user_sessions(user)
        
        logger.info(f"用户 {user.username} 登出成功")
    
    @classmethod
    def refresh_token(cls, refresh_token):
        """
        刷新JWT令牌
        
        Args:
            refresh_token: 刷新令牌
            
        Returns:
            dict: 新的令牌字典
            
        Raises:
            BusinessException: 令牌无效时抛出异常
        """
        try:
            refresh = RefreshToken(refresh_token)
            user_id = refresh.payload.get('user_id')
            
            # 验证用户是否仍然有效
            from apps.users.models import UserProfile
            user = UserProfile.objects.get(id=user_id, is_active=True)
            
            # 生成新的访问令牌
            new_access_token = str(refresh.access_token)
            
            return {
                'access': new_access_token,
                'access_expires_in': settings.SIMPLE_JWT['ACCESS_TOKEN_LIFETIME'].total_seconds(),
            }
            
        except Exception as e:
            logger.warning(f"令牌刷新失败: {str(e)}")
            raise BusinessException(
                code=ErrorCode.TOKEN_INVALID,
                message="刷新令牌无效或已过期"
            )