#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
认证功能测试脚本
"""
import os
import sys
import django
import requests
import json

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

def test_authentication():
    """测试认证功能"""
    base_url = 'http://127.0.0.1:8000/api/auth'
    
    print("=== 认证功能测试 ===")
    
    # 1. 测试获取验证码
    print("\n1. 测试获取验证码...")
    try:
        response = requests.get(f'{base_url}/captcha/')
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
            captcha_key = data['data']['captcha_key']
            print("✓ 验证码获取成功")
        else:
            print(f"✗ 验证码获取失败: {response.text}")
            return
    except Exception as e:
        print(f"✗ 验证码获取异常: {e}")
        return
    
    # 2. 测试登录（使用错误验证码）
    print("\n2. 测试登录（错误验证码）...")
    try:
        login_data = {
            'username': 'testuser',
            'password': 'testpass123',
            'captcha': 'WRONG',
            'captcha_key': captcha_key
        }
        response = requests.post(f'{base_url}/login/', json=login_data)
        print(f"状态码: {response.status_code}")
        data = response.json()
        print(f"响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
        if response.status_code == 400 and data.get('code') == 2002:
            print("✓ 验证码错误检测正常")
        else:
            print("✗ 验证码错误检测异常")
    except Exception as e:
        print(f"✗ 登录测试异常: {e}")
    
    # 3. 获取新验证码并测试正确登录
    print("\n3. 测试正确登录...")
    try:
        # 重新获取验证码
        response = requests.get(f'{base_url}/captcha/')
        if response.status_code == 200:
            data = response.json()
            captcha_key = data['data']['captcha_key']
            
            # 从缓存中获取验证码（这里我们需要手动设置）
            from django.core.cache import cache
            correct_captcha = cache.get(f"captcha:{captcha_key}")
            
            if correct_captcha:
                login_data = {
                    'username': 'testuser',
                    'password': 'testpass123',
                    'captcha': correct_captcha,
                    'captcha_key': captcha_key
                }
                response = requests.post(f'{base_url}/login/', json=login_data)
                print(f"状态码: {response.status_code}")
                data = response.json()
                print(f"响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
                
                if response.status_code == 200 and data.get('code') == 1000:
                    access_token = data['data']['access']
                    print("✓ 登录成功")
                    
                    # 4. 测试获取用户信息
                    print("\n4. 测试获取用户信息...")
                    headers = {'Authorization': f'Bearer {access_token}'}
                    response = requests.get(f'{base_url}/user-info/', headers=headers)
                    print(f"状态码: {response.status_code}")
                    data = response.json()
                    print(f"响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
                    
                    if response.status_code == 200 and data.get('code') == 1000:
                        print("✓ 获取用户信息成功")
                    else:
                        print("✗ 获取用户信息失败")
                    
                    # 5. 测试登出
                    print("\n5. 测试登出...")
                    response = requests.post(f'{base_url}/logout/', headers=headers)
                    print(f"状态码: {response.status_code}")
                    data = response.json()
                    print(f"响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
                    
                    if response.status_code == 200 and data.get('code') == 1000:
                        print("✓ 登出成功")
                    else:
                        print("✗ 登出失败")
                        
                else:
                    print("✗ 登录失败")
            else:
                print("✗ 无法获取验证码")
        else:
            print("✗ 无法获取新验证码")
    except Exception as e:
        print(f"✗ 登录测试异常: {e}")
    
    print("\n=== 测试完成 ===")

if __name__ == '__main__':
    test_authentication()